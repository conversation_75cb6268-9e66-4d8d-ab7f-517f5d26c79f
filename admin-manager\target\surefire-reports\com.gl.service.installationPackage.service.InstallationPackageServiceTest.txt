-------------------------------------------------------------------------------
Test set: com.gl.service.installationPackage.service.InstallationPackageServiceTest
-------------------------------------------------------------------------------
Tests run: 23, Failures: 1, Errors: 6, Skipped: 0, Time elapsed: 2.717 s <<< FAILURE! - in com.gl.service.installationPackage.service.InstallationPackageServiceTest
testList_DatabaseException  Time elapsed: 0.116 s  <<< FAILURE!
org.mockito.exceptions.verification.opentest4j.ArgumentsAreDifferent: 

Argument(s) are different! Wanted:
jdbcTemplate.queryForObject(
    <any string>,
    class java.lang.Long,
    <any java.lang.Object[]>
);
-> at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:906)
Actual invocations have different arguments:
jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1  and ( p.version_name like ? ) GROUP BY p.id  ) t",
    class java.lang.Long,
    "%test%"
);
-> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)

	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_DatabaseException(InstallationPackageServiceTest.java:542)

testList_EmptyResult_Success  Time elapsed: 0.005 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1  and ( p.version_name like ? ) GROUP BY p.id  ) t",
    class java.lang.Long,
    "%test%"
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_EmptyResult_Success(InstallationPackageServiceTest.java:163)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_EmptyResult_Success(InstallationPackageServiceTest.java:170)

testList_NullDto_Success  Time elapsed: 0.007 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t",
    class java.lang.Long
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_NullDto_Success(InstallationPackageServiceTest.java:188)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_NullDto_Success(InstallationPackageServiceTest.java:200)

testList_ExportMode_Success  Time elapsed: 0.004 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1  and ( p.version_name like ? ) GROUP BY p.id  ) t",
    class java.lang.Long,
    "%test%"
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_ExportMode_Success(InstallationPackageServiceTest.java:211)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_ExportMode_Success(InstallationPackageServiceTest.java:224)

testAddOrUpdate_NullParameter_Fail  Time elapsed: 0.202 s  <<< ERROR!
com.gl.framework.exception.CustomException: 获取用户信息异常
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testAddOrUpdate_NullParameter_Fail(InstallationPackageServiceTest.java:306)

testList_WithoutSearchCondition_Success  Time elapsed: 0 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t",
    class java.lang.Long
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_WithoutSearchCondition_Success(InstallationPackageServiceTest.java:134)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_WithoutSearchCondition_Success(InstallationPackageServiceTest.java:148)

testList_WithSearchCondition_Success  Time elapsed: 0.001 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1  and ( p.version_name like ? ) GROUP BY p.id  ) t",
    class java.lang.Long,
    "%test%"
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject("", null, null);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_WithSearchCondition_Success(InstallationPackageServiceTest.java:93)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_WithSearchCondition_Success(InstallationPackageServiceTest.java:107)

